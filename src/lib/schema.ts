import {
  pgTable,
  text,
  varchar,
  timestamp,
  boolean,
  integer,
  serial,
  real,
  json,
  pgEnum,
  primaryKey,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Enums
export const userPlanEnum = pgEnum('UserPlan', ['FREE', 'PRO', 'ENTERPRISE']);
export const unitEnum = pgEnum('Unit', ['MM', 'CM', 'M', 'IN', 'FT']);
export const grainDirectionEnum = pgEnum('GrainDirection', ['NONE', 'HORIZONTAL', 'VERTICAL', 'EITHER']);

// Better-Auth tables (from the migration file)
export const user = pgTable('user', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  email: text('email').notNull().unique(),
  emailVerified: boolean('emailVerified').notNull(),
  image: text('image'),
  createdAt: timestamp('createdAt').notNull(),
  updatedAt: timestamp('updatedAt').notNull(),
});

export const session = pgTable('session', {
  id: text('id').primaryKey(),
  expiresAt: timestamp('expiresAt').notNull(),
  token: text('token').notNull().unique(),
  createdAt: timestamp('createdAt').notNull(),
  updatedAt: timestamp('updatedAt').notNull(),
  ipAddress: text('ipAddress'),
  userAgent: text('userAgent'),
  userId: text('userId').notNull().references(() => user.id),
});

export const account = pgTable('account', {
  id: text('id').primaryKey(),
  accountId: text('accountId').notNull(),
  providerId: text('providerId').notNull(),
  userId: text('userId').notNull().references(() => user.id),
  accessToken: text('accessToken'),
  refreshToken: text('refreshToken'),
  idToken: text('idToken'),
  accessTokenExpiresAt: timestamp('accessTokenExpiresAt'),
  refreshTokenExpiresAt: timestamp('refreshTokenExpiresAt'),
  scope: text('scope'),
  password: text('password'),
  createdAt: timestamp('createdAt').notNull(),
  updatedAt: timestamp('updatedAt').notNull(),
});

export const verification = pgTable('verification', {
  id: text('id').primaryKey(),
  identifier: text('identifier').notNull(),
  value: text('value').notNull(),
  expiresAt: timestamp('expiresAt').notNull(),
  createdAt: timestamp('createdAt'),
  updatedAt: timestamp('updatedAt'),
});

// Application tables (converted from Prisma schema)
export const users = pgTable('users', {
  id: text('id').primaryKey(),
  email: text('email').notNull().unique(),
  username: text('username').unique(),
  name: text('name'),
  avatar: text('avatar'),
  plan: userPlanEnum('plan').default('FREE'),
  password: text('password').notNull(),
  emailVerified: timestamp('emailVerified'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
});

export const projects = pgTable('projects', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  sawKerf: real('sawKerf').default(3.0).notNull(),
  kerfUnit: unitEnum('kerfUnit').default('MM').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
});

export const materials = pgTable('materials', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  length: real('length').notNull(),
  width: real('width').notNull(),
  thickness: real('thickness'),
  unit: unitEnum('unit').default('MM').notNull(),
  quantity: integer('quantity').default(1).notNull(),
  cost: real('cost'),
  supplier: text('supplier'),
  notes: text('notes'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
});

export const pieces = pgTable('pieces', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  length: real('length').notNull(),
  width: real('width').notNull(),
  thickness: real('thickness'),
  unit: unitEnum('unit').default('MM').notNull(),
  quantity: integer('quantity').default(1).notNull(),
  grainDirection: grainDirectionEnum('grainDirection').default('NONE').notNull(),
  priority: integer('priority').default(1).notNull(),
  notes: text('notes'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().notNull(),
  projectId: text('projectId').notNull().references(() => projects.id, { onDelete: 'cascade' }),
});

export const optimizationResults = pgTable('optimization_results', {
  id: text('id').primaryKey(),
  algorithm: text('algorithm').notNull(),
  efficiency: real('efficiency').notNull(),
  wastePercentage: real('wastePercentage').notNull(),
  totalSheets: integer('totalSheets').notNull(),
  totalCost: real('totalCost'),
  processingTime: integer('processingTime').notNull(),
  metadata: json('metadata'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  projectId: text('projectId').notNull().references(() => projects.id, { onDelete: 'cascade' }),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
});

export const optimizationMaterials = pgTable('optimization_materials', {
  id: text('id').primaryKey(),
  quantity: integer('quantity').notNull(),
  materialSnapshot: json('materialSnapshot').notNull(),
  optimizationResultId: text('optimizationResultId').notNull().references(() => optimizationResults.id, { onDelete: 'cascade' }),
  materialId: text('materialId').notNull().references(() => materials.id, { onDelete: 'cascade' }),
});

export const sheets = pgTable('sheets', {
  id: text('id').primaryKey(),
  sheetIndex: integer('sheetIndex').notNull(),
  widthUsed: real('widthUsed').notNull(),
  heightUsed: real('heightUsed').notNull(),
  materialSnapshot: json('materialSnapshot').notNull(),
  optimizationResultId: text('optimizationResultId').notNull().references(() => optimizationResults.id, { onDelete: 'cascade' }),
});

export const placedPieces = pgTable('placed_pieces', {
  id: text('id').primaryKey(),
  x: real('x').notNull(),
  y: real('y').notNull(),
  packedWidth: real('packedWidth').notNull(),
  packedHeight: real('packedHeight').notNull(),
  rotation: real('rotation').default(0).notNull(),
  color: text('color'),
  pieceSnapshot: json('pieceSnapshot').notNull(),
  sheetId: text('sheetId').notNull().references(() => sheets.id, { onDelete: 'cascade' }),
  pieceId: text('pieceId').notNull().references(() => pieces.id, { onDelete: 'cascade' }),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  projects: many(projects),
  materials: many(materials),
  optimizationResults: many(optimizationResults),
}));

export const projectsRelations = relations(projects, ({ one, many }) => ({
  user: one(users, { fields: [projects.userId], references: [users.id] }),
  pieces: many(pieces),
  optimizationResults: many(optimizationResults),
}));

export const materialsRelations = relations(materials, ({ one, many }) => ({
  user: one(users, { fields: [materials.userId], references: [users.id] }),
  usedInOptimizations: many(optimizationMaterials),
}));

export const piecesRelations = relations(pieces, ({ one, many }) => ({
  project: one(projects, { fields: [pieces.projectId], references: [projects.id] }),
  placedPieces: many(placedPieces),
}));

export const optimizationResultsRelations = relations(optimizationResults, ({ one, many }) => ({
  project: one(projects, { fields: [optimizationResults.projectId], references: [projects.id] }),
  user: one(users, { fields: [optimizationResults.userId], references: [users.id] }),
  materials: many(optimizationMaterials),
  sheets: many(sheets),
}));

export const optimizationMaterialsRelations = relations(optimizationMaterials, ({ one }) => ({
  optimizationResult: one(optimizationResults, { fields: [optimizationMaterials.optimizationResultId], references: [optimizationResults.id] }),
  material: one(materials, { fields: [optimizationMaterials.materialId], references: [materials.id] }),
}));

export const sheetsRelations = relations(sheets, ({ one, many }) => ({
  optimizationResult: one(optimizationResults, { fields: [sheets.optimizationResultId], references: [optimizationResults.id] }),
  placedPieces: many(placedPieces),
}));

export const placedPiecesRelations = relations(placedPieces, ({ one }) => ({
  sheet: one(sheets, { fields: [placedPieces.sheetId], references: [sheets.id] }),
  piece: one(pieces, { fields: [placedPieces.pieceId], references: [pieces.id] }),
}));

// Better-Auth relations
export const sessionRelations = relations(session, ({ one }) => ({
  user: one(user, { fields: [session.userId], references: [user.id] }),
}));

export const accountRelations = relations(account, ({ one }) => ({
  user: one(user, { fields: [account.userId], references: [user.id] }),
}));
