import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/database';
import { pieces, projects } from '@/lib/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';
import { nanoid } from 'nanoid';

// Validation schema for piece creation
const CreatePieceSchema = z.object({
  name: z.string().min(1, 'Piece name is required'),
  length: z.number().positive('Length must be positive'),
  width: z.number().positive('Width must be positive'),
  thickness: z.number().positive().optional(),
  unit: z.enum(['MM', 'CM', 'M', 'IN', 'FT']).default('MM'),
  quantity: z.number().int().positive().default(1),
  grainDirection: z.enum(['NONE', 'HORIZONTAL', 'VERTICAL', 'EITHER']).default('NONE'),
  priority: z.number().int().min(1).max(10).default(1),
  notes: z.string().optional(),
  projectId: z.string().min(1, 'Project ID is required'),
});

// POST /api/pieces - Create a new piece
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: request.headers
    });

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = CreatePieceSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const pieceData = validationResult.data;

    // Verify that the project belongs to the user
    const project = await db.query.projects.findFirst({
      where: and(
        eq(projects.id, pieceData.projectId),
        eq(projects.userId, session.user.id)
      ),
    });

    if (!project) {
      return NextResponse.json(
        { success: false, error: 'Project not found' },
        { status: 404 }
      );
    }

    // Create new piece
    const newPiece = await db.insert(pieces).values({
      id: nanoid(),
      name: pieceData.name,
      length: pieceData.length,
      width: pieceData.width,
      thickness: pieceData.thickness || null,
      unit: pieceData.unit,
      quantity: pieceData.quantity,
      grainDirection: pieceData.grainDirection,
      priority: pieceData.priority,
      notes: pieceData.notes || null,
      projectId: pieceData.projectId,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    return NextResponse.json({
      success: true,
      piece: newPiece[0],
    });

  } catch (error) {
    console.error('Error creating piece:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
