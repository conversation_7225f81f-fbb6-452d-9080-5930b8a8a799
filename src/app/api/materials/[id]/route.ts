import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/database';
import { materials } from '@/lib/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';

// Validation schema for material update
const UpdateMaterialSchema = z.object({
  name: z.string().min(1, 'Material name is required').optional(),
  length: z.number().positive('Length must be positive').optional(),
  width: z.number().positive('Width must be positive').optional(),
  thickness: z.number().positive().optional(),
  unit: z.enum(['MM', 'CM', 'M', 'IN', 'FT']).optional(),
  quantity: z.number().int().positive().optional(),
  cost: z.number().min(0).optional(),
  supplier: z.string().optional(),
  notes: z.string().optional(),
});

// PUT /api/materials/[id] - Update a material
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: request.headers
    });

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = UpdateMaterialSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const updateData = validationResult.data;

    // Update material
    const updatedMaterial = await db.update(materials)
      .set({
        ...updateData,
        updatedAt: new Date(),
      })
      .where(and(
        eq(materials.id, params.id),
        eq(materials.userId, session.user.id)
      ))
      .returning();

    if (updatedMaterial.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Material not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      material: updatedMaterial[0],
    });

  } catch (error) {
    console.error('Error updating material:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/materials/[id] - Delete a material
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: request.headers
    });

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Delete material
    const deletedMaterial = await db.delete(materials)
      .where(and(
        eq(materials.id, params.id),
        eq(materials.userId, session.user.id)
      ))
      .returning();

    if (deletedMaterial.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Material not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Material deleted successfully',
    });

  } catch (error) {
    console.error('Error deleting material:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
