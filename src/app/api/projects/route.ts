import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/database';
import { projects, pieces } from '@/lib/schema';
import { eq, desc } from 'drizzle-orm';
import { z } from 'zod';
import { nanoid } from 'nanoid';

// Validation schema for project creation
const CreateProjectSchema = z.object({
  name: z.string().min(1, 'Project name is required'),
  description: z.string().optional(),
  sawKerf: z.number().min(0, 'Saw kerf must be non-negative').default(3.0),
  kerfUnit: z.enum(['MM', 'CM', 'M', 'IN', 'FT']).default('MM'),
});

// GET /api/projects - Get user's projects
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: request.headers
    });

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's projects with piece count
    const userProjects = await db.query.projects.findMany({
      where: eq(projects.userId, session.user.id),
      orderBy: [desc(projects.updatedAt)],
      with: {
        pieces: true,
      },
    });

    // Transform data to include piece count
    const projectsWithCount = userProjects.map(project => ({
      ...project,
      pieceCount: project.pieces.length,
      pieces: undefined, // Remove pieces array to reduce payload size
    }));

    return NextResponse.json({
      success: true,
      projects: projectsWithCount,
    });

  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/projects - Create a new project
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: request.headers
    });

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = CreateProjectSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const projectData = validationResult.data;

    // Create new project
    const newProject = await db.insert(projects).values({
      id: nanoid(),
      name: projectData.name,
      description: projectData.description || null,
      sawKerf: projectData.sawKerf,
      kerfUnit: projectData.kerfUnit,
      userId: session.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    return NextResponse.json({
      success: true,
      project: newProject[0],
    });

  } catch (error) {
    console.error('Error creating project:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
